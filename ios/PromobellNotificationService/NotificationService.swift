import UserNotifications

class NotificationService: UNNotificationServiceExtension {

  var contentHandler: ((UNNotificationContent) -> Void)?
  var bestAttemptContent: UNMutableNotificationContent?

  override func didReceive(_ request: UNNotificationRequest,
                           withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
    self.contentHandler = contentHandler
    bestAttemptContent = (request.content.mutableCopy() as? UNMutableNotificationContent)

    print("🔔 Extensão NotificationService rodou. Payload recebido: \(request.content.userInfo)")

    guard let bestAttemptContent = bestAttemptContent else {
      contentHandler(request.content)
      return
    }

    // tenta fcm_options.image (APNs) e, se não houver, data.image
    var imageURLString: String?
    if let fcmOptions = bestAttemptContent.userInfo["fcm_options"] as? [AnyHashable: Any],
       let img = fcmOptions["image"] as? String {
      imageURLString = img
    } else if let dataImg = bestAttemptContent.userInfo["image"] as? String {
      imageURLString = dataImg
    }

    guard let s = imageURLString, let url = URL(string: s) else {
      contentHandler(bestAttemptContent)
      return
    }

    URLSession.shared.downloadTask(with: url) { tempURL, _, _ in
      defer { contentHandler(bestAttemptContent) }
      guard let tempURL = tempURL else { return }

      let tmpDir = URL(fileURLWithPath: NSTemporaryDirectory())
      let fileURL = tmpDir.appendingPathComponent(url.lastPathComponent)
      try? FileManager.default.removeItem(at: fileURL)

      do {
        try FileManager.default.copyItem(at: tempURL, to: fileURL)
        if let att = try? UNNotificationAttachment(identifier: "image", url: fileURL) {
          bestAttemptContent.attachments = [att]
        }
      } catch {
        // segue sem imagem
      }
    }.resume()
  }

  override func serviceExtensionTimeWillExpire() {
    if let handler = contentHandler, let content = bestAttemptContent {
      handler(content)
    }
  }
}
