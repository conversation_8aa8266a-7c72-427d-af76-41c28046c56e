import UserNotifications

class NotificationService: UNNotificationServiceExtension {

  var contentHandler: ((UNNotificationContent) -> Void)?
  var bestAttemptContent: UNMutableNotificationContent?

  override func didReceive(_ request: UNNotificationRequest,
                           withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
    self.contentHandler = contentHandler
    bestAttemptContent = (request.content.mutableCopy() as? UNMutableNotificationContent)

    print("🔔 Extensão NotificationService rodou. Payload recebido: \(request.content.userInfo)")

    guard let bestAttemptContent = bestAttemptContent else {
      contentHandler(request.content)
      return
    }

    // Procura pela imagem em diferentes locais do payload
    var imageURLString: String?

    // 1. Primeiro tenta no data (onde você está enviando)
    if let dataImg = bestAttemptContent.userInfo["image"] as? String {
      imageURLString = dataImg
      print("🖼️ Imagem encontrada em data.image: \(dataImg)")
    }
    // 2. Tenta em fcm_options.image
    else if let fcmOptions = bestAttemptContent.userInfo["fcm_options"] as? [AnyHashable: Any],
            let img = fcmOptions["image"] as? String {
      imageURLString = img
      print("🖼️ Imagem encontrada em fcm_options.image: \(img)")
    }
    // 3. Tenta diretamente no userInfo (caso o Firebase mova para lá)
    else if let directImg = bestAttemptContent.userInfo["gcm.notification.image"] as? String {
      imageURLString = directImg
      print("🖼️ Imagem encontrada em gcm.notification.image: \(directImg)")
    }

    guard let s = imageURLString, !s.isEmpty, let url = URL(string: s) else {
      print("❌ Nenhuma URL de imagem válida encontrada")
      contentHandler(bestAttemptContent)
      return
    }

    print("📥 Baixando imagem de: \(url)")

    URLSession.shared.downloadTask(with: url) { [weak self] tempURL, response, error in
      defer {
        if let handler = self?.contentHandler, let content = self?.bestAttemptContent {
          handler(content)
        }
      }

      if let error = error {
        print("❌ Erro ao baixar imagem: \(error)")
        return
      }

      guard let tempURL = tempURL, let bestAttemptContent = self?.bestAttemptContent else {
        print("❌ URL temporária ou conteúdo não disponível")
        return
      }

      let tmpDir = URL(fileURLWithPath: NSTemporaryDirectory())
      let fileName = url.lastPathComponent.isEmpty ? "notification_image.jpg" : url.lastPathComponent
      let fileURL = tmpDir.appendingPathComponent(fileName)

      // Remove arquivo existente se houver
      try? FileManager.default.removeItem(at: fileURL)

      do {
        try FileManager.default.copyItem(at: tempURL, to: fileURL)

        // Cria o attachment
        let attachment = try UNNotificationAttachment(identifier: "image", url: fileURL, options: [
          UNNotificationAttachmentOptionsTypeHintKey: "public.jpeg"
        ])

        bestAttemptContent.attachments = [attachment]
        print("✅ Imagem anexada com sucesso: \(fileName)")

      } catch {
        print("❌ Erro ao processar imagem: \(error)")
      }
    }.resume()
  }

  override func serviceExtensionTimeWillExpire() {
    print("⏰ Tempo da extensão expirando...")
    if let handler = contentHandler, let content = bestAttemptContent {
      handler(content)
    }
  }
}
