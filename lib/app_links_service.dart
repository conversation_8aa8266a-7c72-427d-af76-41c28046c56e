import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/models/categorias_menu.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'src/models/product.dart';
import 'src/services/deep_link_handler.dart';
import 'src/services/logs/app_logger.dart';

class AppLinksService extends ChangeNotifier {
  static bool hasReceivedLink = false;
  final _appLinks = AppLinks();

  final _deepLinkHandler = DeepLinkHandler();

  ValueNotifier<Product> produtos = ValueNotifier<Product>(
    Product.empty(),
  );

  Future<void> initAppLinks() async {
    try {
      hasReceivedLink = false;

      final uri = await _appLinks.getInitialLink();
      if (uri != null && !hasReceivedLink) {
        _handleAppLink(uri);
      }

      _appLinks.uriLinkStream.listen(
        (uri) {
          _handleAppLink(uri);
        },
        onError: (err) {
          AppLogger.logError(
            '❌ AppLinksService: Erro ao processar link',
            err,
            StackTrace.current,
          );
        },
      );
    } catch (e) {
      AppLogger.logError(
        '❌ AppLinksService: Erro ao inicializar serviço de deep links',
        e,
        StackTrace.current,
      );
    }
  }

  void _handleAppLink(Uri uri) async {
    hasReceivedLink = true;
    bool linkProcessed = false;

    try {
      String normalizedPath = uri.path.toLowerCase();
      if (normalizedPath.endsWith('/')) {
        normalizedPath = normalizedPath.substring(
          0,
          normalizedPath.length - 1,
        );
      }
      if (normalizedPath.startsWith('/')) {
        normalizedPath = normalizedPath.substring(1);
      }

      if (normalizedPath == 'product') {
        final String? id = uri.queryParameters['id'];
        if (id != null) {
          // Usa o sistema de rotas do app_module para deep links
          Modular.to.pushNamed('/product', arguments: {'id': id});
          linkProcessed = true;
          return;
        }
      } else if (normalizedPath == 'category') {
        final String? idParam = uri.queryParameters['id'];
        final String? categoryName = uri.queryParameters['name'];

        Map<String, dynamic> arguments = {};

        if (idParam != null) {
          final int id = int.tryParse(idParam) ?? -1;
          final categoria = CategoriaMenu.getCategoriaById(id);
          if (categoria != null) {
            arguments['categoria'] = categoria;
            arguments['id'] = idParam;
          }
        }

        if (categoryName != null && categoryName.isNotEmpty) {
          try {
            final CategoriaMenu categoriaByName = CategoriaMenu.getCategoriaByNome(categoryName);
            arguments['categoria'] = categoriaByName;
            arguments['name'] = categoryName;
          } catch (e) {
            // Erro ao buscar categoria por nome
          }
        }

        // Usa o sistema de rotas do app_module para deep links (igual ao produto)
        Modular.to.pushNamed('/category', arguments: arguments);
        linkProcessed = true;
        return;
      }
    } catch (e) {
      AppLogger.logError(
        '❌ AppLinksService: Erro ao processar link de categoria',
        e,
        StackTrace.current,
      );
    }

    // Só processa com o handler padrão se não foi processado acima
    if (!linkProcessed) {
      await _deepLinkHandler.processDeepLink(uri);
    }
  }

  Future<Product> getProduct(String id) async {
    final int idProduto = int.parse(id);
    final Product product = await getProduto(idProduto);
    return product;
  }

  Future<Product> getProduto(int idProduto) async {
    SupabaseClient supabase = Supabase.instance.client;
    try {
      final data = await supabase.from('produtos_cadastro').select().eq('id', idProduto).select();
      final Product produto = Product.fromMap(data.first);

      return produto;
    } catch (e, stackTrace) {
      AppLogger.logError('Erro ao buscar produto', e, stackTrace);
      rethrow;
    }
  }
}
