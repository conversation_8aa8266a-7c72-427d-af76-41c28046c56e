import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../../app_links_service.dart';
import '../../../../../firebase_messaging_service.dart';
import '../../../../app/app_module.dart';
import '../../../../models/user_profile_model.dart';
import '../../../../services/logs/app_logger.dart';
import '../../../../services/navigation/scroll_services.dart';
import '../../../../services/supabase/usuarios/put/put_usuarios.dart';

class BaseController with ChangeNotifier {
  final PutUsuarios _putUsuarios;
  final ScrollService _scrollService;

  BaseController({
    required PutUsuarios putUsuarios,
    required ScrollService scrollService,
  }) : _putUsuarios = putUsuarios,
       _scrollService = scrollService;

  int index = 0;
  PageController pageController = PageController();
  final ValueNotifier<int> pageIndexNotifier = ValueNotifier<int>(0);
  Function()? resetSearch;

  void navPage(int currentIndex) {
    if (currentIndex < 0 || currentIndex > 2) return;

    if (index != currentIndex) {
      switch (index) {
        case 0:
          _scrollService.saveScrollPosition('offersPage');
          break;
        case 1:
          _scrollService.saveScrollPosition('categoriesPage');
          break;
      }
    }

    index = currentIndex;
    pageIndexNotifier.value = currentIndex;
    if (resetSearch != null) {
      resetSearch!();
    }

    if (pageController.hasClients) {
      pageController.jumpToPage(currentIndex);
    }

    // Usar addPostFrameCallback para evitar notifyListeners durante build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  final FirebaseMessagingService firebaseMessagingService = FirebaseMessagingService();

  void navBackFromCategoryDeepLink() {
    AppLinksService.hasReceivedLink = false;

    // Navega para a home com o índice 0 (ofertas) igual ao produto
    Modular.to
        .pushNamedAndRemoveUntil(
          '/home',
          (route) => false,
          arguments: {
            'initialIndex': 0, // Índice da página de ofertas (igual ao produto)
            'maintainIndex': false,
          },
        )
        .then((_) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            navPage(0); // Navega para o índice de ofertas na HomeBasePage
          });
        });
  }

  void navBackFromProductDeepLink() {
    AppLinksService.hasReceivedLink = false;

    Modular.to
        .pushNamedAndRemoveUntil(
          AppModule.homeRoute,
          (route) => false,
          arguments: {'initialIndex': 0, 'maintainIndex': false},
        )
        .then((_) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            navPage(0);
          });
        });
  }

  Future<void> tokenFCM(UserProfileModel user) async {
    try {
      String token = await firebaseMessagingService.getToken() ?? '';
      if (token.isEmpty) return;

      if (user.tokenFirebase != token) {
        await _putUsuarios.updateUser(
          UserProfileModel(
            name: user.name,
            email: user.email,
            image: user.image,
            imageId: user.imageId,
            tokenFirebase: token,
          ),
        );
      }
    } catch (e) {
      AppLogger.logError(
        'Erro ao atualizar token FCM',
        e,
        StackTrace.current,
      );
    }
  }
}
