import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_offline/flutter_offline.dart';
import 'package:lottie/lottie.dart';

import '../../../../theme/color_outlet.dart';
import '../../../components/text_pattern.dart';

class NoConnect extends StatefulWidget {
  final Widget child;
  const NoConnect({super.key, required this.child});

  @override
  State<NoConnect> createState() => _NoConnectState();
}

class _NoConnectState extends State<NoConnect> {
  bool _isCheckingConnection = false;

  Future<void> _checkConnection() async {
    setState(() {
      _isCheckingConnection = true;
    });

    final connectivityResult = await Connectivity().checkConnectivity();
    final bool connected = !connectivityResult.contains(
      ConnectivityResult.none,
    );

    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _isCheckingConnection = false;
      });

      if (connected) {
        setState(() {});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return OfflineBuilder(
      connectivityBuilder:
          (
            BuildContext context,
            connectivity,
            Widget child,
          ) {
            bool connected = !connectivity.contains(ConnectivityResult.none);

            return connected
                ? child
                : Stack(
                    fit: StackFit.expand,
                    children: [
                      child,
                      Positioned(
                        height: MediaQuery.of(context).size.height,
                        left: 0.0,
                        right: 0.0,
                        child: Material(
                          color: ColorOutlet.surface,
                          child: Container(
                            height: MediaQuery.of(context).size.height,
                            width: MediaQuery.of(context).size.width,
                            color: Colors.transparent,
                            child: Column(
                              children: [
                                SizedBox(
                                  height:
                                      MediaQuery.of(context).size.height * 0.3,
                                ),
                                Lottie.asset(
                                  'assets/lottie/lottie-wifi.json',
                                  width: 120,
                                  height: 120,
                                  fit: BoxFit.cover,
                                ),
                                const SizedBox(height: 24),
                                TextPattern.customText(
                                  text: 'Parece que você está offline',
                                  fontSize: 16,
                                  fontWeightOption: FontWeightOption.semiBold,
                                ),
                                const SizedBox(height: 16),
                                SizedBox(
                                  width: 300,
                                  child: TextPattern.customText(
                                    text:
                                        'Verifique sua conexão e tente novamente para continuar navegando no aplicativo',
                                    fontSize: 14,
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                const SizedBox(height: 24),
                                ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: ColorOutlet.surface,
                                    foregroundColor:
                                        ColorOutlet.contentTertiary,
                                    overlayColor: ColorOutlet
                                        .systemBorderDisabled
                                        .withValues(alpha: 0.3),
                                    minimumSize: const Size(300, 56),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                        16,
                                      ),
                                      side: BorderSide(
                                        color: ColorOutlet.contentDisabled,
                                        width: 1,
                                      ),
                                    ),
                                  ),
                                  onPressed: _isCheckingConnection
                                      ? null
                                      : _checkConnection,
                                  child: _isCheckingConnection
                                      ? SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2.5,
                                            color: ColorOutlet.contentSecondary,
                                          ),
                                        )
                                      : TextPattern.customText(
                                          text: 'Atualizar',
                                          fontSize: 14,
                                          color: ColorOutlet.contentSecondary,
                                        ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
          },
      child: widget.child,
    );
  }
}
