import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import 'custom_dialog.dart';

class FollowButton extends StatelessWidget {
  final bool isFollowing;
  final VoidCallback? onFollow;
  final VoidCallback? onUnfollow;
  final String text;

  const FollowButton({
    this.text = 'Seguir',
    required this.isFollowing,
    this.onFollow,
    this.onUnfollow,
    super.key,
  });

  void _handlePress(BuildContext context) {
    if (isFollowing) {
      CustomDialog.show(
        context,
        onConfirm: onUnfollow ?? () {},
        onCancel: onUnfollow ?? () {},
      );
    } else {
      onFollow?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      child: ElevatedButton(
        onPressed: () => _handlePress(context),
        style: ElevatedButton.styleFrom(
          elevation: 0,
          shadowColor: Colors.transparent,
          backgroundColor: _buttonColor,
          overlayColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          padding: EdgeInsets.zero,
        ),
        child: isFollowing ? _buildIcon() : _buildText(),
      ),
    );
  }

  Color get _buttonColor =>
      isFollowing ? ColorOutlet.feedbackDisabled : ColorOutlet.contentPrimary;

  Widget _buildIcon() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SvgPicture.asset(SvgIcons.actionNotification, height: 20, width: 20),
        SizedBox(width: 8),
        TextPattern.customText(
          text: 'Seguindo',
          fontSize: 14,
          color: ColorOutlet.contentSecondary,
        ),
      ],
    );
  }

  Widget _buildText() {
    return TextPattern.customText(
      text: text,
      fontSize: 14,
      color: ColorOutlet.contentTertiary,
    );
  }
}

class FollowButtonTop extends StatelessWidget {
  final bool isFollowing;
  final VoidCallback? onFollow;
  final VoidCallback? onUnfollow;
  final String text;
  final bool modal;

  const FollowButtonTop({
    this.text = 'Seguir',
    required this.isFollowing,
    this.modal = false,
    this.onFollow,
    this.onUnfollow,
    super.key,
  });

  void _handlePress(BuildContext context) {
    if (isFollowing) {
      CustomDialog.show(
        context,
        onConfirm: onUnfollow ?? () {},
        onCancel: onUnfollow ?? () {},
      );
    } else {
      onFollow?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      width: isFollowing ? 40 : null,
      child: ElevatedButton(
        onPressed: () => _handlePress(context),
        style: ElevatedButton.styleFrom(
          elevation: 0,
          shadowColor: Colors.transparent,
          backgroundColor: ColorOutlet.paper,
          overlayColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
          padding: isFollowing
              ? EdgeInsets.zero
              : EdgeInsets.symmetric(horizontal: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: isFollowing
            ? SvgPicture.asset(
                SvgIcons.actionNotification,
                height: 24,
                width: 24,
              )
            : _buildText(),
      ),
    );
  }

  Widget _buildText() {
    return TextPattern.customText(
      text: text,
      fontSize: 14,
      color: ColorOutlet.contentSecondary,
    );
  }
}
