import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';
import 'package:promobell/src/modules/offers/controllers/story/story_controller.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../core/base/controllers/base_controller/base_controller.dart';
import '../../../../../models/categorias_menu.dart';
import '../../../../../services/navigation/scroll_services.dart';
import '../../../../categories/categories_module.dart';
import '../../../controllers/offers_controller.dart';
import 'category_image_badg.dart';

class CategoryHeaderWidget extends StatefulWidget {
  final CategoriaMenu categoria;

  const CategoryHeaderWidget({super.key, required this.categoria});

  @override
  State<CategoryHeaderWidget> createState() => _CategoryHeaderWidgetState();
}

class _CategoryHeaderWidgetState extends State<CategoryHeaderWidget> {
  OffersController controller = Modular.get<OffersController>();
  final BaseController controllerBase = Modular.get<BaseController>();
  final scrollService = Modular.get<ScrollService>();
  StoryController storyController = Modular.get<StoryController>();

  @override
  void initState() {
    super.initState();
    controller.fetchFollowersCount(widget.categoria.id);
    controller.fetchFollowStatus(
      widget.categoria.id,
      Supabase.instance.client.auth.currentUser?.email ?? '',
    );
  }

  void toggleFollow() {
    controller
        .toggleFollowCategory(
          widget.categoria.id,
          Supabase.instance.client.auth.currentUser?.email ?? '',
          widget.categoria.nome,
        )
        .then((_) {
          storyController.refreshStory();
        });
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        InkWell(
          splashColor: ColorOutlet.systemBorderDisabled.withValues(
            alpha: 0.3,
          ),
          highlightColor: ColorOutlet.systemBorderDisabled.withValues(
            alpha: 0.3,
          ),
          onTap: () {
            // Salva a posição do scroll da página atual
            scrollService.saveScrollPosition('offersPage');
            Modular.to.pushNamed(
              '/categories${CategoriesModule.detailsCategoryPage}',
              arguments: widget.categoria,
            );
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              CategoryImageBadg(categoria: widget.categoria),
              SizedBox(
                width: 8,
              ), // Espaçamento entre o contêiner e o texto
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      TextPattern.customText(
                        text: widget.categoria.nome,
                        color: ColorOutlet.contentTertiary,
                        fontSize: 20,
                        fontWeightOption: FontWeightOption.bold,
                      ),
                      const SizedBox(width: 4),
                      SvgPicture.asset(
                        SvgIcons.markerVerifiedFilled,
                        width: 20,
                        height: 20,
                        colorFilter: const ColorFilter.mode(
                          ColorOutlet.contentTertiary,
                          BlendMode.srcIn,
                        ),
                      ),
                    ],
                  ),
                  TextPattern.customText(
                    text: numberLikes(
                      controller
                          .getFollowersCount(widget.categoria.id)
                          .toString(),
                    ),
                    color: ColorOutlet.contentTertiary,
                    fontSize: 12,
                    fontWeightOption: FontWeightOption.medium,
                  ),
                ],
              ),
            ],
          ),
        ),
        Container(
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: controller.isCategoryFollowed(widget.categoria.id)
                ? Border.all(color: Colors.transparent, width: 1)
                : Border.all(
                    color: ColorOutlet.contentTertiary,
                    width: 1,
                  ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 8.0, sigmaY: 8.0),
              child: Container(
                // color: ColorOutlet.contentSecondary.withAlpha(50),
                color: controller.isCategoryFollowed(widget.categoria.id)
                    ? ColorOutlet.contentSecondary.withAlpha(50)
                    : Colors.transparent,
                child: ElevatedButton(
                  onPressed: toggleFollow, // Chama o método com debounce
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.only(left: 16, right: 16),
                    elevation: 0,
                    backgroundColor: Colors.transparent,
                    overlayColor: ColorOutlet.systemBorderDisabled.withValues(
                      alpha: 0.3,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: TextPattern.customText(
                    text:
                        controller.isCategoryFollowed(
                          widget.categoria.id,
                        )
                        ? 'Seguindo'
                        : 'Seguir',
                    fontSize: 14,
                    color: ColorOutlet.contentTertiary,
                    fontWeightOption: FontWeightOption.semiBold,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  String numberLikes(String likes) {
    int likesInt = int.parse(likes);
    if (likesInt == 1) {
      return '$likes seguidor';
    } else if (likesInt < 1000) {
      return '$likes seguidores';
    } else if (likesInt < 1000000) {
      return '${(likesInt / 1000).toStringAsFixed(1)}K seguidores';
    } else if (likesInt < 1000000000) {
      return '${(likesInt / 1000000).toStringAsFixed(1)}M seguidores';
    } else {
      return '${(likesInt / 1000000000).toStringAsFixed(1)}B seguidores';
    }
  }
}
