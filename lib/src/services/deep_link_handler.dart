import 'package:flutter_modular/flutter_modular.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../core/base/controllers/base_controller/base_controller.dart';
import '../models/product.dart';
import '../modules/offers/offers_module.dart';
import 'logs/app_logger.dart';

class DeepLinkHandler {
  static final DeepLinkHandler _instance = DeepLinkHandler._internal();

  final baseController = Modular.get<BaseController>();

  factory DeepLinkHandler() {
    return _instance;
  }

  DeepLinkHandler._internal();

  Future<void> processDeepLink(Uri uri) async {
    String normalizedPath = uri.path.toLowerCase();
    if (normalizedPath.endsWith('/')) {
      normalizedPath = normalizedPath.substring(
        0,
        normalizedPath.length - 1,
      );
    }

    if (normalizedPath == 'product') {
      await _handleProductDeepLink(uri);
    } else if (normalizedPath == 'category') {
      // Lógica movida para app_links_service.dart
      // await _handleCategoryDeepLink(uri);
    } else if (normalizedPath == 'app') {
      await _handleAppDeepLink();
    } else {
      Modular.to.navigate('/home');
    }
  }

  Future<void> _handleProductDeepLink(Uri uri) async {
    try {
      final String? id = uri.queryParameters['id'];

      if (id == null || id.isEmpty || id == "0") {
        Modular.to.navigate('/home');
        return;
      }

      final product = await _getProduct(id);

      Modular.to.pushNamed(
        '/offers${OffersModule.productDetails}',
        arguments: product,
      );
    } catch (e) {
      Modular.to.navigate('/home');
    }
  }

  Future<void> _handleAppDeepLink() async {
    try {
      // Navegar para a tela de ofertas (índice 0 da home)
      Modular.to.navigate('/home', arguments: {
        'initialIndex': 0,
        'maintainIndex': false,
      });
    } catch (e) {
      Modular.to.navigate('/home');
    }
  }

  // Future<void> _handleCategoryDeepLink(Uri uri) async {
  //   try {
  //     baseController.navPage(1);

  //     final String? idParam = uri.queryParameters['id'];
  //     if (idParam != null) {
  //       final int id = int.tryParse(idParam) ?? -1;
  //       final categoria = CategoriaMenu.getCategoriaById(id);

  //       if (categoria != null) {
  //         Modular.to.navigate(
  //           '/categories${CategoriesModule.detailsCategoryPage}',
  //           arguments: categoria,
  //         );
  //         return;
  //       } else {
  //         AppLogger.logInfo(
  //           '⚠️ Categoria não encontrada para o ID: $id',
  //         );
  //       }
  //     }

  //     final String? categoryName = uri.queryParameters['name'];
  //     if (categoryName != null && categoryName.isNotEmpty) {
  //       final CategoriaMenu categoria = CategoriaMenu.getCategoriaByNome(
  //         categoryName,
  //       );
  //       Modular.to.navigate(
  //         '/categories${CategoriesModule.detailsCategoryPage}',
  //         arguments: categoria,
  //       );
  //       return;
  //     }

  //     Modular.to.navigate('/categories');
  //   } catch (e, stackTrace) {
  //     AppLogger.logError(
  //       'Erro ao processar deep link de categoria',
  //       e,
  //       stackTrace,
  //     );

  //     baseController.navPage(1);
  //     Modular.to.navigate('/categories');
  //   }
  // }

  Future<Product> _getProduct(String id) async {
    final int idProduto = int.parse(id);
    final supabase = Supabase.instance.client;

    try {
      final data = await supabase.from('produtos_cadastro').select().eq('id', idProduto).select();

      if (data.isEmpty) {
        throw Exception('Produto não encontrado');
      }

      final Product produto = Product.fromMap(data.first);
      return produto;
    } catch (e, stackTrace) {
      AppLogger.logError('Erro ao buscar produto', e, stackTrace);
      rethrow;
    }
  }
}
